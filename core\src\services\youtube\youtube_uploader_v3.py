"""
YouTube上传器 V3
基于基类的简化版本
"""

import os
import logging
import asyncio

from ..common.base_uploader import BaseUploader
from ..common.workflow_engine import WorkflowEngine

logger = logging.getLogger(__name__)


class YouTubeUploaderV3(BaseUploader):
    """YouTube上传器V3类"""

    def __init__(self, device_id: str, appium_server: str = 'http://localhost:4723'):
        """初始化YouTube上传器

        Args:
            device_id: 设备ID
            appium_server: Appium服务器地址
        """
        super().__init__(
            device_id=device_id,
            app_package='com.google.android.youtube',
            app_activity='com.google.android.youtube.app.honeycomb.Shell$HomeActivity',
            appium_server=appium_server
        )

        # 初始化配置驱动的元素查找器和工作流引擎
        # 获取项目根目录路径
        current_file = os.path.abspath(__file__)
        logger.info(f"当前文件路径: {current_file}")

        # 从 core/src/services/youtube/youtube_uploader_v3.py 到 core/config/platforms/youtube/
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
        config_dir = os.path.join(project_root, 'config', 'platforms', 'youtube')
        elements_config_path = os.path.join(config_dir, 'elements.yaml')

        logger.info(f"项目根目录: {project_root}")
        logger.info(f"配置目录: {config_dir}")
        logger.info(f"元素配置文件路径: {elements_config_path}")
        logger.info(f"元素配置文件是否存在: {os.path.exists(elements_config_path)}")

        # 初始化工作流引擎
        self.workflow_engine = WorkflowEngine(elements_config_path)
        self.workflow_engine.set_progress_callback(self._on_progress_update)
        self.workflow_engine.set_status_callback(self._on_status_update)

        # 进度回调函数（由任务执行器设置）
        self.progress_callback = None
        self.status_callback = None

        # 工作流配置路径
        self.workflows_dir = os.path.join(config_dir, 'workflows')
        logger.info(f"工作流配置目录: {self.workflows_dir}")
        logger.info(f"工作流配置目录是否存在: {os.path.exists(self.workflows_dir)}")

    def set_progress_callback(self, callback):
        """设置进度回调函数

        Args:
            callback: 进度回调函数，接收(progress, message)参数
        """
        self.progress_callback = callback

    def set_status_callback(self, callback):
        """设置状态回调函数

        Args:
            callback: 状态回调函数，接收(status, message)参数
        """
        self.status_callback = callback

    def _on_progress_update(self, progress: int, message: str):
        """工作流引擎进度更新回调

        Args:
            progress: 进度百分比
            message: 进度消息
        """
        # 更新本地状态
        self.update_progress(progress, message)

        # 如果有外部回调，调用它
        if self.progress_callback:
            try:
                self.progress_callback(progress, message)
            except Exception as e:
                logger.error(f"调用进度回调失败: {str(e)}")

    def _on_status_update(self, status: str, message: str):
        """工作流引擎状态更新回调

        Args:
            status: 状态
            message: 状态消息
        """
        # 更新本地状态
        self.set_status(status, message)

        # 如果有外部回调，调用它
        if self.status_callback:
            try:
                self.status_callback(status, message)
            except Exception as e:
                logger.error(f"调用状态回调失败: {str(e)}")

    def get_app_name(self) -> str:
        """获取应用名称

        Returns:
            str: 应用名称
        """
        return "YouTube"

    async def verify_app_launched(self) -> bool:
        """验证YouTube应用是否已启动

        Returns:
            bool: 是否已启动
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                logger.error("无法获取Appium驱动")
                return False

            logger.info("开始验证YouTube应用启动状态...")

            # 首先尝试重新建立Appium连接，解决AccessibilityNodeInfo超时问题
            await self._fix_appium_connection_if_needed(driver)

            # 方法1：检查当前应用包名（最可靠的方法）
            try:
                current_package = driver.current_package
                logger.info(f"当前应用包名: {current_package}")
                if current_package == "com.google.android.youtube":
                    logger.info("✅ YouTube应用启动成功（通过包名验证）")
                    return True
                else:
                    logger.warning(f"⚠️ 当前应用不是YouTube，而是: {current_package}")
                    # 不立即返回False，尝试其他验证方法
            except Exception as e:
                logger.warning(f"获取当前包名失败: {str(e)}")
                # 不立即返回False，尝试其他验证方法

            # 方法2：检查活动名称作为备用
            try:
                current_activity = driver.current_activity
                logger.info(f"当前活动: {current_activity}")
                if "youtube" in current_activity.lower():
                    logger.info("✅ YouTube应用启动成功（通过活动名验证）")
                    return True
                else:
                    logger.warning(f"⚠️ 当前活动不是YouTube相关: {current_activity}")
            except Exception as e:
                logger.warning(f"获取当前活动失败: {str(e)}")

            # 如果所有验证方法都失败
            logger.error("❌ 所有验证方法都失败，YouTube应用未正确启动")
            return False

        except Exception as e:
            logger.error(f"验证YouTube应用启动状态异常: {str(e)}")
            return False

    async def _fix_appium_connection_if_needed(self, driver) -> bool:
        """修复Appium连接问题（如果需要）

        Args:
            driver: Appium驱动

        Returns:
            bool: 是否修复成功
        """
        try:
            logger.info("检查Appium连接状态...")

            # 尝试一个简单的操作来测试连接
            try:
                _ = driver.current_package
                logger.info("Appium连接正常")
                return True
            except Exception as e:
                logger.warning(f"Appium连接异常: {str(e)}")

                # 如果是AccessibilityNodeInfo超时问题，尝试重启Appium会话
                if "AccessibilityNodeInfo" in str(e) or "Timed out" in str(e):
                    logger.info("检测到AccessibilityNodeInfo超时问题，尝试重新建立连接...")

                    # 重新启动应用
                    try:
                        driver.terminate_app("com.google.android.youtube")
                        await asyncio.sleep(2)
                        driver.activate_app("com.google.android.youtube")
                        await asyncio.sleep(3)

                        # 测试连接是否恢复
                        _ = driver.current_package
                        logger.info("✅ Appium连接已恢复")
                        return True
                    except Exception as restart_e:
                        logger.error(f"重启应用失败: {str(restart_e)}")
                        return False
                else:
                    logger.error(f"未知的Appium连接问题: {str(e)}")
                    return False

        except Exception as e:
            logger.error(f"修复Appium连接异常: {str(e)}")
            return False

    async def execute_upload_task(self, video_path: str, title: str, description: str, privacy: str, content_type: str = "video", selected_music: list = None, metadata: dict = None) -> bool:
        """执行YouTube上传任务

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            privacy: 隐私设置（public, unlisted, private）
            content_type: 内容类型（video, shorts, live, post）
            selected_music: 选中的音乐列表
            metadata: 任务元数据（包含音频设置等配置）

        Returns:
            bool: 是否上传成功
        """
        try:
            logger.info("===== 开始执行YouTube上传任务 =====")
            logger.info(f"视频路径: {video_path}")
            logger.info(f"视频标题: {title}")
            logger.info(f"隐私设置: {privacy}")
            logger.info(f"内容类型: {content_type}")
            logger.info(f"选中的音乐: {selected_music}")
            logger.info(f"选中音乐数量: {len(selected_music) if selected_music else 0}")

            # 更新上传状态
            self.set_status("uploading", "正在准备上传...")
            self.update_progress(0, "正在准备上传...")

            # 检查文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                self.set_status("error", f"视频文件不存在: {video_path}")
                return False

            # 启动YouTube应用（包含网络连接检查和V2rayN启动）
            if not await self._launch_youtube_with_network_check():
                return False
            self.update_progress(20, "YouTube应用已启动")

            # 🔧 智能文件传输：检查是否需要重新传输文件
            if not await self._smart_prepare_media_file(video_path):
                return False
            self.update_progress(30, "视频文件已准备就绪")

            # 执行上传流程
            return await self._perform_youtube_upload(video_path, title, description, privacy, content_type, selected_music, metadata)

        except Exception as e:
            logger.error(f"执行上传任务异常: {str(e)}", exc_info=True)
            self.set_status("error", f"执行上传任务异常: {str(e)}")
            return False

    async def _smart_prepare_media_file(self, video_path: str) -> bool:
        """智能文件准备：检查文件状态并决定是否需要传输

        Args:
            video_path: 视频文件路径

        Returns:
            bool: 是否准备成功
        """
        try:
            logger.info(f"🔍 智能文件准备: {video_path}")

            # 1. 检查本地文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"❌ 本地视频文件不存在: {video_path}")
                self.set_status("error", f"本地视频文件不存在: {video_path}")
                return False

            logger.info(f"✅ 本地文件存在: {os.path.basename(video_path)}")

            # 2. 检查设备上是否已有文件
            device_file_exists = await self._check_device_file_exists(video_path)

            if device_file_exists:
                logger.info(f"✅ 设备上已存在文件，跳过传输步骤")
                self.update_progress(25, "检测到设备上已有文件，跳过传输")

                # 🔧 重要修复：即使跳过传输，也要设置current_upload_file用于后续清理
                self.current_upload_file = os.path.basename(video_path)
                logger.info(f"📁 设置当前文件用于清理: {self.current_upload_file}")

                # 即使文件已存在，也验证是否在相册中可见
                logger.info(f"📱 验证已存在的视频是否在相册中可见...")
                self.update_progress(27, "验证视频在相册中的可见性")

                gallery_verification = await self._verify_video_in_gallery(video_path)
                if gallery_verification:
                    logger.info(f"✅ 已存在视频在相册中验证成功")
                    self.update_progress(30, "视频文件已准备就绪并在相册中可见")
                else:
                    logger.warning(f"⚠️ 已存在视频在相册中验证失败，但继续执行")
                    self.update_progress(30, "视频文件已存在（相册验证失败但继续执行）")

                return True

            # 3. 如果设备上没有文件，执行正常的文件传输
            logger.info(f"📤 设备上没有文件，开始传输...")
            self.update_progress(22, "开始传输视频文件到设备")

            success = await self.prepare_media_file(video_path)
            if success:
                logger.info(f"✅ 文件传输成功")

                # 4. 验证文件是否在相册中可见
                logger.info(f"📱 验证视频是否在相册中可见...")
                self.update_progress(27, "验证视频在相册中的可见性")

                gallery_verification = await self._verify_video_in_gallery(video_path)
                if gallery_verification:
                    logger.info(f"✅ 视频在相册中验证成功")
                    self.update_progress(30, "视频文件已准备就绪并在相册中可见")
                    return True
                else:
                    logger.warning(f"⚠️ 视频在相册中验证失败，但继续执行")
                    self.update_progress(30, "视频文件已传输（相册验证失败但继续执行）")
                    return True  # 即使相册验证失败也继续执行
            else:
                logger.error(f"❌ 文件传输失败")
                return False

        except Exception as e:
            logger.error(f"智能文件准备异常: {str(e)}", exc_info=True)
            self.set_status("error", f"智能文件准备异常: {str(e)}")
            return False

    async def _check_device_file_exists(self, video_path: str) -> bool:
        """检查设备上是否已存在视频文件

        Args:
            video_path: 本地视频文件路径

        Returns:
            bool: 设备上是否存在文件
        """
        try:
            # 获取文件名
            filename = os.path.basename(video_path)

            # 设备上的目标路径
            device_path = f"/sdcard/Pictures/{filename}"

            logger.info(f"🔍 检查设备文件: {device_path}")

            # 使用adb检查文件是否存在
            if self.device_manager:
                # 获取真实设备ID用于ADB操作
                device_id_for_adb = getattr(self.device_manager, 'real_device_id', None) or self.device_manager.device_id
                if device_id_for_adb:
                    import subprocess

                    try:
                        # 执行adb shell ls命令检查文件，使用UTF-8编码
                        result = subprocess.run([
                            "adb", "-s", device_id_for_adb,
                            "shell", "ls", device_path
                        ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)

                    if result.returncode == 0 and device_path in result.stdout:
                        logger.info(f"✅ 设备上存在文件: {device_path}")
                        return True
                    else:
                        logger.info(f"❌ 设备上不存在文件: {device_path}")
                        return False
                except UnicodeDecodeError as e:
                    logger.warning(f"⚠️ 文件名编码问题，尝试更精确的检查: {str(e)}")
                    # 如果文件名有编码问题，尝试通过文件大小来验证
                    try:
                        # 获取本地文件大小
                        local_file_size = os.path.getsize(video_path)
                        logger.info(f"🔍 本地文件大小: {local_file_size} 字节")

                        # 列出设备上Pictures目录的所有mp4文件及其大小
                        result = subprocess.run([
                            "adb", "-s", device_id_for_adb,
                            "shell", "ls", "-la", "/sdcard/Pictures/*.mp4"
                        ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)

                        if result.returncode == 0 and result.stdout.strip():
                            # 解析文件列表，查找相同大小的文件
                            lines = result.stdout.strip().split('\n')
                            for line in lines:
                                parts = line.split()
                                if len(parts) >= 5:
                                    try:
                                        device_file_size = int(parts[4])  # 文件大小在第5列
                                        if device_file_size == local_file_size:
                                            logger.info(f"✅ 通过文件大小匹配找到目标文件: {device_file_size} 字节")
                                            return True
                                    except (ValueError, IndexError):
                                        continue

                            logger.info(f"❌ 设备上没有找到相同大小的文件")
                            return False
                        else:
                            logger.info(f"❌ 设备上没有mp4文件")
                            return False
                    except Exception as e2:
                        logger.warning(f"⚠️ 精确检查也失败: {str(e2)}")
                        return False
            else:
                logger.warning(f"⚠️ 设备管理器未初始化，无法检查设备文件")
                return False

        except subprocess.TimeoutExpired:
            logger.warning(f"⚠️ 检查设备文件超时，假设文件不存在")
            return False
        except Exception as e:
            logger.warning(f"⚠️ 检查设备文件异常: {str(e)}，假设文件不存在")
            return False

    async def launch_app(self) -> bool:
        """重写基类的launch_app方法，使用YouTube特定的网络检查逻辑

        Returns:
            bool: 是否成功启动
        """
        return await self._launch_youtube_with_network_check()

    async def _launch_youtube_with_network_check(self) -> bool:
        """启动YouTube应用（包含网络连接检查和V2rayN启动）

        Returns:
            bool: 是否成功启动
        """
        try:
            logger.info("===== 开始启动YouTube应用 =====")
            # 显示原始设备ID和真实设备ID
            real_device_id = getattr(self.device_manager, 'real_device_id', None)
            logger.info(f"原始设备ID: {self.device_manager.device_id}")
            if real_device_id:
                logger.info(f"真实设备ID: {real_device_id}")
            else:
                logger.info("真实设备ID: 未映射")

            # 先检查网络连接
            logger.info("检查网络连接状态...")
            network_status = await self.network_manager.check_network_connection()
            logger.info(f"网络状态: {network_status}")

            # 如果无法访问Google，尝试启动V2rayN（无论是否能访问基础网络）
            if not network_status.get("google_accessible", False):
                if network_status.get("basic_network", False):
                    logger.info("无法访问Google但可以访问基础网络，尝试启动V2rayN...")
                    self.upload_message = "网络连接受限，正在启动V2rayN代理..."
                else:
                    logger.info("无法访问Google和基础网络，尝试启动V2rayN...")
                    self.upload_message = "网络连接异常，正在启动V2rayN代理..."

                # 启动V2rayN
                v2ray_success = await self.v2ray_manager.launch_and_connect()
                if v2ray_success:
                    logger.info("V2rayN启动成功，等待代理生效...")
                    await asyncio.sleep(5)  # 等待代理生效

                    # 重新检查Google连接
                    logger.info("重新检查Google连接...")
                    network_status = await self.network_manager.check_network_connection()
                    logger.info(f"启动V2rayN后的网络状态: {network_status}")

                    if network_status.get("google_accessible", False):
                        logger.info("启动V2rayN后可以访问Google，网络连接正常")
                        self.upload_message = "V2rayN启动成功，网络连接正常"
                    else:
                        logger.warning("启动V2rayN后仍无法访问Google，但继续执行")
                        self.upload_message = "V2rayN已启动，但网络连接仍受限，继续执行"

                        # 再次尝试启动V2rayN
                        logger.info("再次尝试启动V2rayN...")
                        await asyncio.sleep(5)
                        v2ray_success = await self.v2ray_manager.launch_and_connect()
                        if v2ray_success:
                            await asyncio.sleep(3)
                            # 第三次检查Google连接
                            network_status = await self.network_manager.check_network_connection()
                            if network_status.get("google_accessible", False):
                                logger.info("第二次启动V2rayN后可以访问Google")
                                self.upload_message = "V2rayN启动成功，网络连接正常"
                            else:
                                logger.warning("多次尝试后仍无法访问Google，但继续执行")
                                self.upload_message = "网络连接受限，但继续执行"
                else:
                    logger.warning("V2rayN启动失败，但继续执行")
                    self.upload_message = "V2rayN启动失败，网络连接受限，但继续执行"
            else:
                logger.info("网络连接正常，可以访问Google")
                self.upload_message = "网络连接正常"

            # 确保网络连接检查和V2rayN启动已完成
            logger.info("网络连接检查和V2rayN启动已完成，现在启动YouTube应用")
            await asyncio.sleep(3)  # 等待一段时间确保V2rayN生效

            # 启动YouTube应用
            success = await self.device_manager.launch_app(self.app_package, self.app_activity)
            if not success:
                logger.error("启动YouTube应用失败")
                self.set_status("error", "启动YouTube应用失败")
                return False

            # 验证应用是否成功启动
            if not await self.verify_app_launched():
                logger.error("YouTube应用未成功启动")
                self.set_status("error", "YouTube应用未成功启动")
                return False

            logger.info("YouTube应用已成功启动")
            self.upload_message = "YouTube应用已启动"
            return True

        except Exception as e:
            logger.error(f"启动YouTube应用异常: {str(e)}", exc_info=True)
            self.set_status("error", f"启动YouTube应用异常: {str(e)}")
            return False

    async def _perform_youtube_upload(self, video_path: str, title: str, description: str, privacy: str, content_type: str = "video", selected_music: list = None, metadata: dict = None) -> bool:
        """执行YouTube上传流程（工作流驱动版本）

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            privacy: 隐私设置
            content_type: 内容类型（video, shorts, live, post）
            selected_music: 选中的音乐列表
            metadata: 任务元数据（包含音频设置等配置）

        Returns:
            bool: 是否上传成功
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                self.set_status("error", "没有可用的Appium驱动")
                return False

            logger.info("🚀 开始执行YouTube上传流程（工作流驱动版本）...")
            logger.info(f"📋 接收到的参数 - content_type: '{content_type}', type: {type(content_type)}")
            self.update_progress(35, "正在执行上传流程...")

            # 根据内容类型选择工作流
            if content_type == "shorts":
                workflow_file = "shorts_upload.yaml"
                logger.info("✅ 选择短视频工作流")
            else:
                workflow_file = "video_upload.yaml"
                logger.info(f"✅ 选择普通视频工作流（content_type='{content_type}'）")

            workflow_path = os.path.join(self.workflows_dir, workflow_file)
            logger.info(f"使用工作流配置: {workflow_path}")
            logger.info(f"工作流文件是否存在: {os.path.exists(workflow_path)}")

            # 加载工作流
            if not self.workflow_engine.load_workflow(workflow_path):
                logger.error(f"❌ 加载工作流失败: {workflow_path}")
                self.set_status("error", "加载工作流配置失败")
                return False

            # 准备工作流参数
            # 获取设备上的实际文件名（可能是简化后的文件名）
            original_filename = os.path.basename(video_path)
            device_filename = self.file_manager.get_device_filename(original_filename)
            logger.info(f"📁 原始文件名: {original_filename}")
            logger.info(f"📱 设备文件名: {device_filename}")

            workflow_parameters = {
                'filename': device_filename,  # 使用设备上的实际文件名
                'title': title,
                'description': description,
                'privacy': privacy,
                'selectedMusic': selected_music or []  # 传递音乐配置
            }

            # 添加音频设置参数（从metadata中获取）
            if metadata:
                # 音频设置参数
                if 'keepOriginalAudio' in metadata:
                    workflow_parameters['keepOriginalAudio'] = metadata['keepOriginalAudio']
                    logger.info(f"✅ 传递音频设置 - 保留原声: {metadata['keepOriginalAudio']}")

                if 'originalAudioPercentage' in metadata:
                    workflow_parameters['originalAudioPercentage'] = metadata['originalAudioPercentage']
                    logger.info(f"✅ 传递音频设置 - 原声音量: {metadata['originalAudioPercentage']}%")

                if 'musicVolumePercentage' in metadata:
                    workflow_parameters['musicVolumePercentage'] = metadata['musicVolumePercentage']
                    logger.info(f"✅ 传递音频设置 - 背景音乐音量: {metadata['musicVolumePercentage']}%")

                # 其他可能的配置参数
                for key in ['contentType', 'tags', 'category', 'language']:
                    if key in metadata:
                        workflow_parameters[key] = metadata[key]
                        logger.debug(f"传递配置参数: {key} = {metadata[key]}")

            logger.info(f"🔧 完整工作流参数: {workflow_parameters}")
            logger.info(f"🔊 音频设置检查:")
            logger.info(f"  - keepOriginalAudio: {workflow_parameters.get('keepOriginalAudio', '未设置')}")
            logger.info(f"  - originalAudioPercentage: {workflow_parameters.get('originalAudioPercentage', '未设置')}")
            logger.info(f"  - musicVolumePercentage: {workflow_parameters.get('musicVolumePercentage', '未设置')}")

            # 执行工作流
            success = await self.workflow_engine.execute_workflow(driver, **workflow_parameters)

            if success:
                logger.info("🎉 工作流执行成功")
                return True
            else:
                logger.error("❌ 工作流执行失败")
                return False

        except Exception as e:
            logger.error(f"执行YouTube上传流程异常: {str(e)}", exc_info=True)
            self.set_status("error", f"执行YouTube上传流程异常: {str(e)}")
            return False

    async def _verify_video_in_gallery(self, video_path: str) -> bool:
        """验证视频是否在相册中可见

        Args:
            video_path: 视频文件路径

        Returns:
            bool: 是否在相册中可见
        """
        try:
            logger.info("🔍 开始验证视频在相册中的可见性...")

            driver = self.device_manager.get_driver()
            if not driver:
                logger.warning("⚠️ 无法获取Appium驱动，跳过相册验证")
                return False

            # 1. 启动相册应用
            logger.info("📱 启动相册应用...")
            gallery_packages = [
                "com.android.gallery3d",           # 系统相册（优先）
                "com.google.android.apps.photos",  # Google Photos
                "com.miui.gallery",                # MIUI相册
                "com.samsung.android.gallery3d"    # 三星相册
            ]

            gallery_launched = False
            for package in gallery_packages:
                try:
                    logger.info(f"尝试启动相册应用: {package}")
                    success = await self.device_manager.launch_app(package, None)
                    if success:
                        logger.info(f"✅ 成功启动相册应用: {package}")
                        gallery_launched = True
                        await asyncio.sleep(3)  # 等待应用启动
                        break
                except Exception as e:
                    logger.debug(f"启动{package}失败: {str(e)}")
                    continue

            if not gallery_launched:
                logger.warning("⚠️ 无法启动任何相册应用，跳过相册验证")
                return False

            # 2. 查找视频文件
            filename = os.path.basename(video_path)
            device_filename = self.file_manager.get_device_filename(filename)
            logger.info(f"🔍 在相册中查找视频: {device_filename}")

            # 3. 尝试多种方式查找视频
            video_found = False

            # 方式1: 通过文件名查找
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from appium.webdriver.common.appiumby import AppiumBy

                # 查找包含文件名的元素
                video_elements = driver.find_elements(AppiumBy.XPATH, f"//*[contains(@content-desc, '{device_filename}')]")
                if video_elements:
                    logger.info(f"✅ 通过文件名在相册中找到视频: {len(video_elements)}个匹配项")
                    video_found = True
                else:
                    logger.info(f"❌ 通过文件名未在相册中找到视频")
            except Exception as e:
                logger.debug(f"通过文件名查找视频失败: {str(e)}")

            # 方式2: 查找最近添加的视频
            if not video_found:
                try:
                    # 查找视频缩略图或视频相关元素
                    video_elements = driver.find_elements(AppiumBy.XPATH, "//android.widget.ImageView[@clickable='true']")
                    if video_elements:
                        logger.info(f"✅ 在相册中找到{len(video_elements)}个可点击的图像元素（可能包含视频）")
                        video_found = True
                    else:
                        logger.info(f"❌ 在相册中未找到可点击的图像元素")
                except Exception as e:
                    logger.debug(f"查找视频缩略图失败: {str(e)}")

            # 方式3: 检查是否有任何媒体内容
            if not video_found:
                try:
                    # 查找任何媒体相关的元素
                    media_elements = driver.find_elements(AppiumBy.XPATH, "//*[contains(@class, 'Image') or contains(@class, 'Video')]")
                    if media_elements:
                        logger.info(f"✅ 在相册中找到{len(media_elements)}个媒体元素")
                        video_found = True
                    else:
                        logger.info(f"❌ 在相册中未找到任何媒体元素")
                except Exception as e:
                    logger.debug(f"查找媒体元素失败: {str(e)}")

            # 4. 返回YouTube应用
            logger.info("🔄 返回YouTube应用...")
            try:
                await self.device_manager.launch_app(self.app_package, self.app_activity)
                await asyncio.sleep(2)
            except Exception as e:
                logger.warning(f"返回YouTube应用失败: {str(e)}")

            if video_found:
                logger.info("✅ 相册验证成功：视频在相册中可见")
                return True
            else:
                logger.warning("⚠️ 相册验证失败：未在相册中找到视频")
                return False

        except Exception as e:
            logger.warning(f"⚠️ 相册验证异常: {str(e)}")
            # 确保返回YouTube应用
            try:
                await self.device_manager.launch_app(self.app_package, self.app_activity)
                await asyncio.sleep(2)
            except:
                pass
            return False